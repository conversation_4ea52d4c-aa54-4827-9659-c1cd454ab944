// nuxt.config.ts

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  modules: [
    '@nuxt/ui-pro',
    '@nuxtjs/i18n'
  ],
  css: ['~/assets/css/main.css'],
  devtools: { enabled: true },

  i18n: {
    locales: [
      { code: 'en', iso: 'en-US', name: 'English', file: 'en.json' },
      { code: 'th', iso: 'th-TH', name: 'ไทย', file: 'th.json' }
    ],
    lazy: true,
    langDir: 'app/locales/', // ใช้แบบ relative path ได้เลย
    defaultLocale: 'th',
    // ลบบรรทัดนี้ออกไป: vueI18n: './i18n.config.ts'
  }
})

<template>
  <UContainer class="border-t border-gray-200 dark:border-gray-800 mt-20">
    <div class="flex items-center justify-between py-8">
      <span class="text-sm text-gray-500">© 2024 My App. All rights reserved.</span>
      
      <div class="flex space-x-6">
        <NuxtLink
          v-for="link in footerLinks"
          :key="link.to"
          :to="link.to"
          class="text-sm text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
        >
          {{ link.label }}
        </NuxtLink>
      </div>
    </div>
  </UContainer>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'

const { t } = useI18n()

const footerLinks = computed(() => [
  { label: t('privacy'), to: '/privacy' },
  { label: t('terms'), to: '/terms' }
])
</script>

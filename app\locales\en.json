{"welcome": "Welcome", "home": "Home", "about": "About", "contact": "Contact", "privacy": "Privacy", "terms": "Terms", "getStarted": "Get Started", "viewOnGitHub": "View on GitHub", "buildSomethingAmazing": "Build Something Amazing", "heroDescription": "Create beautiful, responsive applications with Nuxt UI Pro. The most comprehensive Vue component library built for modern web development.", "features": {"modernDesign": {"title": "Modern Design", "description": "Beautiful components built with Tailwind CSS and modern design principles."}, "typeSafety": {"title": "Type Safety", "description": "Full TypeScript support with intelligent autocompletion."}, "darkMode": {"title": "Dark Mode", "description": "Built-in dark mode support for all components."}, "responsive": {"title": "Responsive", "description": "Mobile-first responsive design that works everywhere."}}}
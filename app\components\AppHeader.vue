<template>
  <UContainer class="border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between h-16">
      <!-- Logo -->
      <div class="flex items-center space-x-2">
        <UIcon name="i-heroicons-code-bracket" class="w-6 h-6 text-primary-500" />
        <span class="font-bold text-lg">My App</span>
      </div>

      <!-- Navigation Links -->
      <nav class="hidden md:flex space-x-6">
        <NuxtLink
          v-for="link in links"
          :key="link.to"
          :to="link.to"
          class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
        >
          {{ link.label }}
        </NuxtLink>
      </nav>

      <!-- Right Side -->
      <div class="flex items-center space-x-4">
        <!-- ปุ่มเปลี่ยนภาษา -->
        <UButton
          :icon="locale === 'th' ? 'i-heroicons-flag-th-20-solid' : 'i-heroicons-flag-us-20-solid'" // เปลี่ยน icon ตามภาษา
          variant="ghost"
          color="gray"
          @click="toggleLocale"
        />

        <UButton
          icon="i-heroicons-moon"
          variant="ghost"
          color="gray"
          @click="toggleColorMode"
        />
        <UButton variant="soft" color="primary">
          {{ $t("getStarted") }} <!-- เปลี่ยนเป็นใช้ข้อความจาก i18n -->
        </UButton>
      </div>
    </div>
  </UContainer>
</template>

<script setup>
import { useI18n } from 'vue-i18n' // import useI18n

const colorMode = useColorMode()
const { locale, setLocale } = useI18n() // ดึง locale และ setLocale มาใช้งาน

const toggleColorMode = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

// ฟังก์ชันสำหรับสลับภาษา
const toggleLocale = () => {
  setLocale(locale.value === 'th' ? 'en' : 'th')
}

const links = [
  { label: 'Home', to: '/' },
  { label: 'About', to: '/about' },
  { label: 'Contact', to: '/contact' }
]
</script>

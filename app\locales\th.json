{"welcome": "ยินดีต้อนรับ", "home": "หน้าหลัก", "about": "เกี่ยวกับ", "contact": "ติดต่อ", "privacy": "ความเป็นส่วนตัว", "terms": "ข้อกำหนด", "getStarted": "เริ่มต้นใช้งาน", "viewOnGitHub": "ดูบน GitHub", "buildSomethingAmazing": "สร้างสรรค์สิ่งที่น่าทึ่ง", "heroDescription": "สร้างแอปพลิเคชันที่สวยงามและตอบสนองได้ดีด้วย Nuxt UI Pro. ไลบรารีคอมโพเนนต์ Vue ที่ครอบคลุมที่สุดสำหรับนักพัฒนาเว็บสมัยใหม่", "features": {"modernDesign": {"title": "การออกแบบที่ทันสมัย", "description": "คอมโพเนนต์ที่สวยงามสร้างด้วย Tailwind CSS และหลักการออกแบบที่ทันสมัย"}, "typeSafety": {"title": "ความปลอดภัยของประเภท", "description": "รองรับ TypeScript เต็มรูปแบบพร้อมการเติมข้อความอัตโนมัติที่ชาญฉลาด"}, "darkMode": {"title": "โหมดมืด", "description": "รองรับโหมดมืดในตัวสำหรับคอมโพเนนต์ทั้งหมด"}, "responsive": {"title": "ตอบสนอง", "description": "การออกแบบที่ตอบสนองแบบ Mobile-first ที่ทำงานได้ทุกที่"}}}
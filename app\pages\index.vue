<template>
  <UContainer class="py-16">
    <!-- Hero Section -->
    <div class="text-center mb-16">
      <UBadge variant="subtle" size="lg" class="mb-4">
        {{ $t('welcome') }} Nuxt UI Pro v3
      </UBadge>
      
      <h1 class="text-4xl md:text-6xl font-bold mb-6 text-gray-900 dark:text-white">
        {{ $t('buildSomethingAmazing') }}
      </h1>
      
      <p class="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
        {{ $t('heroDescription') }}
      </p>
      
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <UButton size="lg" color="primary">
          {{ $t('getStarted') }}
        </UButton>
        <UButton size="lg" variant="ghost" color="gray">
          {{ $t('viewOnGitHub') }}
        </UButton>
      </div>
    </div>

    <!-- Features Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
      <UCard v-for="feature in features" :key="feature.title" class="p-6">
        <div class="flex items-center mb-4">
          <UIcon :name="feature.icon" class="w-8 h-8 text-primary-500 mr-3" />
          <h3 class="text-lg font-semibold">{{ $t(feature.title) }}</h3>
        </div>
        <p class="text-gray-600 dark:text-gray-400">{{ $t(feature.description) }}</p>
      </UCard>
    </div>
  </UContainer>
</template>

<script setup>
// ไม่ต้อง import { useI18n } from 'vue-i18n' เพราะ $t ถูก inject ให้ใช้ได้ทั่วทั้ง template

const features = [
  {
    title: 'features.modernDesign.title',
    description: 'features.modernDesign.description',
    icon: 'i-heroicons-sparkles'
  },
  {
    title: 'features.typeSafety.title',
    description: 'features.typeSafety.description',
    icon: 'i-heroicons-shield-check'
  },
  {
    title: 'features.darkMode.title',
    description: 'features.darkMode.description',
    icon: 'i-heroicons-moon'
  },
  {
    title: 'features.responsive.title',
    description: 'features.responsive.description',
    icon: 'i-heroicons-device-phone-mobile'
  }
]
</script>
